<script setup lang="ts">
    import Switch from '@/components/ui/switch/Switch.vue';
    import { Button } from '@/components/ui/button'
    import AppLayout from '@/layouts/AppLayout.vue';
    import { type BreadcrumbItem } from '@/types';
    import { Head } from '@inertiajs/vue3';


    const breadcrumbs: BreadcrumbItem[] = [
        {
            title: 'User',
            href: '/user',
        },

];

    import { ref } from 'vue';
    const count = ref(0);

</script>

<template>
    <Head title="User" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <Switch />
        <button @click="count++">Count is: {{ count }}</button>
        <div>
            <Button @click="count++">Button Count is: {{ count }}</Button>
        </div>
    </AppLayout>
</template>
