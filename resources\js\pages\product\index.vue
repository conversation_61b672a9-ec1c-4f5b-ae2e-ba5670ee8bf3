<script setup lang="ts">
import { Button } from '@/components/ui/button';
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, usePage } from '@inertiajs/vue3';
import { onMounted } from 'vue';

const page = usePage();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Product',
        href: '/product',
    },
];

onMounted(() => {
    console.log('Page props:', page.props);
    console.log('Flash:', page.props.flash);
    console.log('Message:', page.props.message);
});
</script>

<template>
    <Head title="List of Products" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="p-4">
            <Button @click="$inertia.get('/product/create')" variant="default">Add Product</Button>
        </div>
    </AppLayout>
</template>
