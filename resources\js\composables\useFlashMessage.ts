import { usePage } from '@inertiajs/vue3';
import { watch } from 'vue';
import { toast } from 'vue-sonner';

export function useFlashMessage() {
    const page = usePage();

    watch(
        () => page.props.flash,
        (flash) => {
            if (flash?.success) {
                toast.success(flash.success, { duration: 3000 });
            }
            if (flash?.error) {
                toast.error(flash.error, { duration: 3000 });
            }
        },
        { immediate: true, deep: true },
    );
}
