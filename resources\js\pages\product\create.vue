<script setup lang="ts">
import { Button } from '@/components/ui/button';
import Input from '@/components/ui/input/Input.vue';
import Label from '@/components/ui/label/Label.vue';
import { NumberField, NumberFieldContent, NumberFieldDecrement, NumberFieldIncrement, NumberFieldInput } from '@/components/ui/number-field';
import Textarea from '@/components/ui/textarea/Textarea.vue';
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head, useForm } from '@inertiajs/vue3';

const form = useForm({
    name: '',
    qty: 0,
    price: 0,
    desc: '',
});

const handleSubmit = () => {
    form.post(route('product.store'), {
        preserveScroll: true,
        onFinish: () => form.reset(),
    });
};

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Create Product',
        href: '/product.create',
    },
];
</script>

<template>
    <Head title="Create of Products" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="px-4 py-4">
            <form @submit.prevent="handleSubmit" class="flex w-1/2 flex-col gap-6">
                <div class="grid gap-6">
                    <!-- Name Field -->
                    <div>
                        <Label class="py-2" for="name">Name</Label>
                        <Input v-model="form.name" type="text" placeholder="Name" />
                        <div class="text-sm text-red-500" v-if="form.errors.name">{{ form.errors.name }}</div>
                    </div>

                    <!-- Quantity and Price Fields - Side by Side -->
                    <div class="grid grid-cols-2 gap-4">
                        <!-- Quantity Field -->
                        <div>
                            <NumberField v-model="form.qty" :default-value="1" :min="1">
                                <Label for="qty">Quantity</Label>
                                <NumberFieldContent>
                                    <NumberFieldDecrement />
                                    <NumberFieldInput />
                                    <NumberFieldIncrement />
                                </NumberFieldContent>
                            </NumberField>
                            <div class="text-sm text-red-500" v-if="form.errors.qty">{{ form.errors.qty }}</div>
                        </div>

                        <!-- Price Field -->
                        <div>
                            <NumberField v-model="form.price" :default-value="18000" :min="1000">
                                <Label for="price">Price</Label>
                                <NumberFieldContent>
                                    <NumberFieldDecrement />
                                    <NumberFieldInput />
                                    <NumberFieldIncrement />
                                </NumberFieldContent>
                            </NumberField>
                            <div class="text-sm text-red-500" v-if="form.errors.price">{{ form.errors.price }}</div>
                        </div>
                    </div>

                    <!-- Description Field -->
                    <div class="grid gap-2">
                        <Label for="desc">Description</Label>
                        <Textarea v-model="form.desc" placeholder="Description" rows="4" />
                        <div class="text-sm text-red-500" v-if="form.errors.desc">{{ form.errors.desc }}</div>
                    </div>

                    <!-- Submit Button -->
                    <div class="grid gap-2">
                        <Button type="submit" :disabled="form.processing">Save</Button>
                    </div>
                </div>
            </form>
        </div>
    </AppLayout>
</template>
