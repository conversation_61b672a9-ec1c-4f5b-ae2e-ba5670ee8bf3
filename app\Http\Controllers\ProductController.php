<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ProductController extends Controller
{
    public function index()
    {
        $message = session('success') ? session('success') : null;

        return inertia('product/index', [
            'message' => $message,
            'products' => Product::all()
        ]);
    }
    public function create()
    {
        return Inertia::render('product/create');
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'name' => 'required',
            'qty' => 'required',
            'price' => 'required',
            'desc' => 'required',
        ], [
            'name.required' => 'Nama tidak boleh kosong',
            'qty.required' => 'Quantity tidak boleh kosong',
            'price.required' => 'Harga tidak boleh kosong',
            'desc.required' => 'Deskripsi tidak boleh kosong',
        ]);

        // Product::create($data);

        return redirect()->route('product')->with([
            'message' => 'berhasil',
        ]);
    }
}
